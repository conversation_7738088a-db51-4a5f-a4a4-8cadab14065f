<?php

use App\Http\Controllers\DataController;
use App\Http\Controllers\LiveMeController;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\SelfUidTrackingController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return redirect('/login');
});
//Login
Route::get('/login',[LoginController::class,'viewLogin'])->name('login');
Route::post('/login',[LoginController::class,'postLogin'])->name('post.login');
//Sign Up
Route::get('/register',[LoginController::class,'viewRegister'])->name('register');
Route::post('/register',[LoginController::class,'postRegister'])->name('post.register');
//Logout
Route::get('/logout',[LoginController::class,'logout'])->name('logout');

Route::group(['middleware' => 'user'], function () {
    //users
    Route::get('/users/{id}/setting',[UserController::class,'setting'])->name('users.setting');
    Route::post('/users/update-setting',[UserController::class,'updateSetting'])->name('users.update_setting');
    Route::get('/users/create',[UserController::class,'create'])->name('users.create');
    Route::post('/users/store',[UserController::class,'store'])->name('users.store');
    Route::get('/users/{id}/edit',[UserController::class,'edit'])->name('users.edit');
    Route::post('/users/update',[UserController::class,'update'])->name('users.update');
    Route::get('/users/destroy/{id}',[UserController::class,'destroy'])->name('users.destroy');
    Route::get('/users',[UserController::class,'index'])->name('users.index');

    // SelfUID Tracking routes
    Route::get('/admin/selfuid-tracking', [SelfUidTrackingController::class, 'index'])->name('selfuid-tracking.index');
    Route::get('/admin/selfuid-tracking/daily-stats', [SelfUidTrackingController::class, 'dailyStats'])->name('selfuid-tracking.daily-stats');
    Route::get('/admin/selfuid-tracking/{selfUid}', [SelfUidTrackingController::class, 'show'])->name('selfuid-tracking.show');
    Route::get('/admin/selfuid-tracking/user/{userToken}', [SelfUidTrackingController::class, 'userStats'])->name('selfuid-tracking.user-stats');
    Route::get('/admin/selfuid-tracking/chart-data/{selfUid}', [SelfUidTrackingController::class, 'chartData'])->name('selfuid-tracking.chart-data');
    Route::get('/admin/selfuid-tracking/optimization-stats', [SelfUidTrackingController::class, 'optimizationStats'])->name('selfuid-tracking.optimization-stats');
    Route::post('/admin/selfuid-tracking/cleanup', [SelfUidTrackingController::class, 'cleanup'])->name('selfuid-tracking.cleanup');
});

Route::group(['middleware' => 'login'], function () {
    //user info
    Route::get('/user-dashboard',[UserController::class,'userDashboard'])->name('user.dashboard');
    Route::get('/info',[UserController::class,'info'])->name('info');
    Route::post('/info-update',[UserController::class,'updateInfo'])->name('info.update');
    Route::post('/change-password',[UserController::class,'changePassword'])->name('info.change-password');
    Route::get('/amount',[UserController::class,'getAmount'])->name('amount');
    //data
    Route::get('/data/{token}',[DataController::class,'viewData'])->name('data');
    Route::get('/data-used/{token}',[DataController::class,'dataUsed'])->name('data.used');
    Route::get('/data-create/{token}',[DataController::class,'create'])->name('data.create');
    Route::get('/edit-data/{id}',[DataController::class,'editData'])->name('edit.data');
    Route::post('/update-data/{id}',[DataController::class,'updateData'])->name('update.data');
    Route::get('/delete-data/{id}',[DataController::class,'delete'])->name('delete.data');
    Route::post('/import-data/{token}',[DataController::class,'importData'])->name('import.data');
    //live me
    Route::get('/live-me/{token}',[LiveMeController::class,'viewLiveMe'])->name('liveMe');
    Route::get('/live-me-create/{token}',[LiveMeController::class,'create'])->name('liveMe.create');
    Route::get('/edit-live-me/{id}',[LiveMeController::class,'editData'])->name('edit.liveMe');
    Route::post('/update-live-me/{id}',[LiveMeController::class,'updateData'])->name('update.liveMe');
    Route::get('/delete-live-me/{id}',[LiveMeController::class,'delete'])->name('delete.liveMe');
    Route::post('/import-live/{token}',[LiveMeController::class,'importData'])->name('import.liveMe');
    //Document
    Route::get('/document',[UserController::class,'documentApi'])->name('document');
});
Route::get('/delete-all',[DataController::class,'deleteAll'])->name('delete.all');

