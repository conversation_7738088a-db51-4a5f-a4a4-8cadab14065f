<?php

namespace App\Http\Controllers;

use App\Models\SelfUidTracking;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class SelfUidTrackingController extends Controller
{
    /**
     * Áp dụng filter cho query
     */
    private function applyFilters($query, Request $request)
    {
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }

        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        return $query;
    }

    /**
     * L<PERSON>y thống kê tổng quan
     */
    private function getOverallStats(Request $request)
    {
        // Lấy bản ghi mới nhất của mỗi Self UID trong phạm vi filter
        $latestRecordsQuery = SelfUidTracking::select('self_uid', 'diamond_balance', 'bean_balance', 'created_at')
            ->whereIn('id', function($query) use ($request) {
                $subQuery = $query->select(DB::raw('MAX(id)'))
                    ->from('self_uid_trackings as st')
                    ->groupBy('st.self_uid');

                $this->applyFilters($subQuery, $request);
            });

        $latestRecords = $this->applyFilters($latestRecordsQuery, $request)->get();

        return [
            'uniqueSelfUidsCount' => $latestRecords->count(),
            'totalDiamond' => $latestRecords->sum('diamond_balance'),
            'totalBean' => $latestRecords->sum('bean_balance')
        ];
    }

    /**
     * Lấy thống kê lượng giảm trong một ngày cụ thể
     */
    private function getDailyDecreaseStats($date, Request $request)
    {
        // Lấy tất cả Self UID có hoạt động trong ngày này
        $selfUidsQuery = SelfUidTracking::select('self_uid', 'user_token')
            ->whereDate('created_at', $date)
            ->distinct();

        // Áp dụng filter
        if ($request->filled('user_token')) {
            $selfUidsQuery->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $selfUidsQuery->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        $selfUids = $selfUidsQuery->get();

        $totalDiamondDecrease = 0;
        $totalBeanDecrease = 0;
        $totalDecreaseCount = 0;
        $affectedUidsCount = 0;

        foreach ($selfUids as $uid) {
            // Lấy tất cả records của Self UID này trong ngày, sắp xếp theo thời gian
            $records = SelfUidTracking::where('user_token', $uid->user_token)
                ->where('self_uid', $uid->self_uid)
                ->whereDate('created_at', $date)
                ->orderBy('created_at', 'asc')
                ->get();

            if ($records->count() < 2) {
                continue; // Cần ít nhất 2 records để tính giảm
            }

            $uidHasDecrease = false;
            for ($i = 1; $i < $records->count(); $i++) {
                $previous = $records[$i - 1];
                $current = $records[$i];

                // Tính sự giảm (chỉ tính khi giảm)
                $diamondDecrease = max(0, $previous->diamond_balance - $current->diamond_balance);
                $beanDecrease = max(0, $previous->bean_balance - $current->bean_balance);

                if ($diamondDecrease > 0 || $beanDecrease > 0) {
                    $totalDiamondDecrease += $diamondDecrease;
                    $totalBeanDecrease += $beanDecrease;
                    $totalDecreaseCount++;
                    $uidHasDecrease = true;
                }
            }

            if ($uidHasDecrease) {
                $affectedUidsCount++;
            }
        }

        return [
            'total_diamond_decrease' => $totalDiamondDecrease,
            'total_bean_decrease' => $totalBeanDecrease,
            'total_decrease_count' => $totalDecreaseCount,
            'affected_uids_count' => $affectedUidsCount
        ];
    }

    /**
     * Lấy thống kê lượng tăng trong một ngày cụ thể
     */
    private function getDailyIncreaseStats($date, Request $request)
    {
        // Lấy tất cả Self UID có hoạt động trong ngày này
        $selfUidsQuery = SelfUidTracking::select('self_uid', 'user_token')
            ->whereDate('created_at', $date)
            ->distinct();

        // Áp dụng filter
        if ($request->filled('user_token')) {
            $selfUidsQuery->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $selfUidsQuery->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        $selfUids = $selfUidsQuery->get();

        $totalDiamondIncrease = 0;
        $totalBeanIncrease = 0;
        $totalIncreaseCount = 0;
        $affectedUidsCount = 0;

        foreach ($selfUids as $uid) {
            // Lấy tất cả records của Self UID này trong ngày, sắp xếp theo thời gian
            $records = SelfUidTracking::where('user_token', $uid->user_token)
                ->where('self_uid', $uid->self_uid)
                ->whereDate('created_at', $date)
                ->orderBy('created_at', 'asc')
                ->get();

            if ($records->count() < 2) {
                continue; // Cần ít nhất 2 records để tính tăng
            }

            $uidHasIncrease = false;
            for ($i = 1; $i < $records->count(); $i++) {
                $previous = $records[$i - 1];
                $current = $records[$i];

                // Tính sự tăng (chỉ tính khi tăng)
                $diamondIncrease = max(0, $current->diamond_balance - $previous->diamond_balance);
                $beanIncrease = max(0, $current->bean_balance - $previous->bean_balance);

                if ($diamondIncrease > 0 || $beanIncrease > 0) {
                    $totalDiamondIncrease += $diamondIncrease;
                    $totalBeanIncrease += $beanIncrease;
                    $totalIncreaseCount++;
                    $uidHasIncrease = true;
                }
            }

            if ($uidHasIncrease) {
                $affectedUidsCount++;
            }
        }

        return [
            'total_diamond_increase' => $totalDiamondIncrease,
            'total_bean_increase' => $totalBeanIncrease,
            'total_increase_count' => $totalIncreaseCount,
            'affected_uids_count' => $affectedUidsCount
        ];
    }

    /**
     * Lấy thống kê theo ngày - chỉ tính bản ghi mới nhất của mỗi Self UID trong ngày
     */
    private function getDailyStats(Request $request)
    {
        // Lấy danh sách các ngày có dữ liệu
        $datesQuery = SelfUidTracking::select(DB::raw('DATE(created_at) as date'))
            ->groupBy(DB::raw('DATE(created_at)'))
            ->orderBy('date', 'desc');

        $this->applyFilters($datesQuery, $request);
        $dates = $datesQuery->pluck('date');

        $dailyStats = [];

        foreach ($dates as $date) {
            // Lấy bản ghi mới nhất của mỗi Self UID trong ngày này
            $latestRecordsQuery = SelfUidTracking::select('self_uid', 'diamond_balance', 'bean_balance')
                ->whereIn('id', function($query) use ($date, $request) {
                    $subQuery = $query->select(DB::raw('MAX(id)'))
                        ->from('self_uid_trackings as st')
                        ->whereDate('st.created_at', $date)
                        ->groupBy('st.self_uid');

                    // Áp dụng filter cho subquery (trừ date filter vì đã có whereDate ở trên)
                    if ($request->filled('user_token')) {
                        $subQuery->where('st.user_token', $request->user_token);
                    }
                    if ($request->filled('self_uid')) {
                        $subQuery->where('st.self_uid', 'LIKE', '%' . $request->self_uid . '%');
                    }
                })
                ->whereDate('created_at', $date);

            // Áp dụng filter cho query chính (trừ date filter)
            if ($request->filled('user_token')) {
                $latestRecordsQuery->where('user_token', $request->user_token);
            }
            if ($request->filled('self_uid')) {
                $latestRecordsQuery->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
            }

            $latestRecords = $latestRecordsQuery->get();

            $currentDiamond = $latestRecords->sum('diamond_balance');
            $currentBean = $latestRecords->sum('bean_balance');

            $dailyStats[] = (object) [
                'date' => $date,
                'unique_self_uids' => $latestRecords->count(),
                'total_diamond' => $currentDiamond,
                'total_bean' => $currentBean,
                'total_records' => $latestRecords->count() // Số Self UID duy nhất, không phải tổng bản ghi
            ];
        }

        // Tính sự thay đổi so với ngày trước đó và thống kê giảm trong ngày
        $dailyStatsWithChanges = [];
        for ($i = 0; $i < count($dailyStats); $i++) {
            $current = $dailyStats[$i];
            $diamondChange = 0;
            $beanChange = 0;

            // So sánh với ngày trước đó (index i+1 vì đã sắp xếp desc)
            if ($i + 1 < count($dailyStats)) {
                $previous = $dailyStats[$i + 1];
                $diamondChange = $current->total_diamond - $previous->total_diamond;
                $beanChange = $current->total_bean - $previous->total_bean;
            }

            $current->diamond_change = $diamondChange;
            $current->bean_change = $beanChange;

            // Tính tổng lượng giảm trong ngày này
            $dailyDecrease = $this->getDailyDecreaseStats($current->date, $request);
            $current->daily_diamond_decrease = $dailyDecrease['total_diamond_decrease'];
            $current->daily_bean_decrease = $dailyDecrease['total_bean_decrease'];
            $current->daily_decrease_count = $dailyDecrease['total_decrease_count'];
            $current->affected_uids_count = $dailyDecrease['affected_uids_count'];

            // Tính tổng lượng tăng trong ngày này
            $dailyIncrease = $this->getDailyIncreaseStats($current->date, $request);
            $current->daily_diamond_increase = $dailyIncrease['total_diamond_increase'];
            $current->daily_bean_increase = $dailyIncrease['total_bean_increase'];
            $current->daily_increase_count = $dailyIncrease['total_increase_count'];
            $current->affected_increase_uids_count = $dailyIncrease['affected_uids_count'];

            $dailyStatsWithChanges[] = $current;
        }

        return collect($dailyStatsWithChanges);
    }

    /**
     * Hiển thị thống kê tăng/giảm theo ngày
     */
    public function dailyStats(Request $request)
    {
        $date = $request->get('date');
        $type = $request->get('type'); // 'increase' hoặc 'decrease'

        if (!$date || !in_array($type, ['increase', 'decrease'])) {
            return redirect()->route('selfuid-tracking.index')
                ->with('error', 'Tham số không hợp lệ');
        }

        if ($type === 'increase') {
            $stats = $this->getIncreaseStatsByDate($date, $request);
            $title = 'Thống kê tổng lượng tăng Diamond và Bean';
        } else {
            $stats = $this->getDecreaseStatsByDate($date, $request);
            $title = 'Thống kê tổng lượng giảm Diamond và Bean';
        }

        return view('admin.selfuid-tracking.daily-stats', compact(
            'stats', 'date', 'type', 'title'
        ));
    }

    /**
     * Lấy thống kê tổng lượng giảm diamond và bean theo ngày
     */
    private function getDecreaseStatsByDate($date, Request $request)
    {
        // Tính toán thống kê giảm cho ngày cụ thể
        $query = SelfUidTracking::whereDate('created_at', $date);

        // Áp dụng filter nếu có
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        // Lấy tất cả records trong ngày
        $records = $query->orderBy('self_uid')->orderBy('created_at')->get();


        $totalDiamondDecrease = 0;
        $totalBeanDecrease = 0;
        $totalDecreaseCount = 0;
        $userDetails = [];
        $selfUidDetails = [];

        // Nhóm theo self_uid để tính toán sự thay đổi
        $groupedBySelfUid = $records->groupBy('self_uid');

        foreach ($groupedBySelfUid as $selfUid => $selfUidRecords) {
            $sortedRecords = $selfUidRecords->sortBy('created_at');
            $previousRecord = null;

            $selfUidDiamondDecrease = 0;
            $selfUidBeanDecrease = 0;
            $selfUidDecreaseCount = 0;

            foreach ($sortedRecords as $record) {
                if ($previousRecord) {
                    $diamondChange = $record->diamond_balance - $previousRecord->diamond_balance;
                    $beanChange = $record->bean_balance - $previousRecord->bean_balance;

                    if ($diamondChange < 0) {
                        $selfUidDiamondDecrease += abs($diamondChange);
                        $selfUidDecreaseCount++;
                    }
                    if ($beanChange < 0) {
                        $selfUidBeanDecrease += abs($beanChange);
                        if ($diamondChange >= 0) { // Chỉ đếm 1 lần nếu cả 2 đều giảm
                            $selfUidDecreaseCount++;
                        }
                    }
                }
                $previousRecord = $record;
            }

            if ($selfUidDiamondDecrease > 0 || $selfUidBeanDecrease > 0) {
                $totalDiamondDecrease += $selfUidDiamondDecrease;
                $totalBeanDecrease += $selfUidBeanDecrease;
                $totalDecreaseCount += $selfUidDecreaseCount;

                // Lấy thông tin user từ record đầu tiên
                $firstRecord = $sortedRecords->first();
                $user = User::where('user_token', $firstRecord->user_token)->first();

                $selfUidDetails[] = [
                    'self_uid' => $selfUid,
                    'user_token' => $firstRecord->user_token,
                    'user_name' => $user ? $user->name : 'Unknown',
                    'diamond_decrease' => $selfUidDiamondDecrease,
                    'bean_decrease' => $selfUidBeanDecrease,
                    'decrease_count' => $selfUidDecreaseCount
                ];

                // Cập nhật thống kê user
                $userToken = $firstRecord->user_token;
                if (!isset($userDetails[$userToken])) {
                    $userDetails[$userToken] = [
                        'user_token' => $userToken,
                        'user_name' => $user ? $user->name : 'Unknown',
                        'diamond_decrease' => 0,
                        'bean_decrease' => 0,
                        'decrease_count' => 0,
                        'uid_count' => 0
                    ];
                }
                $userDetails[$userToken]['diamond_decrease'] += $selfUidDiamondDecrease;
                $userDetails[$userToken]['bean_decrease'] += $selfUidBeanDecrease;
                $userDetails[$userToken]['decrease_count'] += $selfUidDecreaseCount;
                $userDetails[$userToken]['uid_count']++;
            }
        }

        // Sắp xếp selfUidDetails theo diamond_decrease giảm dần và lấy top 20
        usort($selfUidDetails, function($a, $b) {
            return $b['diamond_decrease'] - $a['diamond_decrease'];
        });
        $selfUidDetails = array_slice($selfUidDetails, 0, 20);

        // Chuyển userDetails từ associative array thành indexed array và sắp xếp
        $userDetailsArray = array_values($userDetails);
        usort($userDetailsArray, function($a, $b) {
            return $b['diamond_decrease'] - $a['diamond_decrease'];
        });

        return [
            'total_diamond_decrease' => $totalDiamondDecrease,
            'total_bean_decrease' => $totalBeanDecrease,
            'total_decrease_count' => $totalDecreaseCount,
            'affected_uid_count' => count($selfUidDetails),
            'user_details' => array_slice($userDetailsArray, 0, 10), // Top 10 users
            'self_uid_details' => $selfUidDetails // Top 20 Self UIDs
        ];
    }

    /**
     * Lấy thống kê tổng lượng tăng diamond và bean theo ngày
     */
    private function getIncreaseStatsByDate($date, Request $request)
    {
        // Tính toán thống kê tăng cho ngày cụ thể
        $query = SelfUidTracking::whereDate('created_at', $date);

        // Áp dụng filter nếu có
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }
        if ($request->filled('self_uid')) {
            $query->where('self_uid', 'LIKE', '%' . $request->self_uid . '%');
        }

        // Lấy tất cả records trong ngày
        $records = $query->orderBy('self_uid')->orderBy('created_at')->get();

        $totalDiamondIncrease = 0;
        $totalBeanIncrease = 0;
        $totalIncreaseCount = 0;
        $userDetails = [];
        $selfUidDetails = [];

        // Nhóm theo self_uid để tính toán sự thay đổi
        $groupedBySelfUid = $records->groupBy('self_uid');

        foreach ($groupedBySelfUid as $selfUid => $selfUidRecords) {
            $sortedRecords = $selfUidRecords->sortBy('created_at');
            $previousRecord = null;

            $selfUidDiamondIncrease = 0;
            $selfUidBeanIncrease = 0;
            $selfUidIncreaseCount = 0;

            foreach ($sortedRecords as $record) {
                if ($previousRecord) {
                    $diamondChange = $record->diamond_balance - $previousRecord->diamond_balance;
                    $beanChange = $record->bean_balance - $previousRecord->bean_balance;

                    if ($diamondChange > 0) {
                        $selfUidDiamondIncrease += $diamondChange;
                        $selfUidIncreaseCount++;
                    }
                    if ($beanChange > 0) {
                        $selfUidBeanIncrease += $beanChange;
                        if ($diamondChange <= 0) { // Chỉ đếm 1 lần nếu cả 2 đều tăng
                            $selfUidIncreaseCount++;
                        }
                    }
                }
                $previousRecord = $record;
            }

            if ($selfUidDiamondIncrease > 0 || $selfUidBeanIncrease > 0) {
                $totalDiamondIncrease += $selfUidDiamondIncrease;
                $totalBeanIncrease += $selfUidBeanIncrease;
                $totalIncreaseCount += $selfUidIncreaseCount;

                // Lấy thông tin user từ record đầu tiên
                $firstRecord = $sortedRecords->first();
                $user = User::where('user_token', $firstRecord->user_token)->first();

                $selfUidDetails[] = [
                    'self_uid' => $selfUid,
                    'user_token' => $firstRecord->user_token,
                    'user_name' => $user ? $user->name : 'Unknown',
                    'diamond_increase' => $selfUidDiamondIncrease,
                    'bean_increase' => $selfUidBeanIncrease,
                    'increase_count' => $selfUidIncreaseCount
                ];

                // Cập nhật thống kê user
                $userToken = $firstRecord->user_token;
                if (!isset($userDetails[$userToken])) {
                    $userDetails[$userToken] = [
                        'user_token' => $userToken,
                        'user_name' => $user ? $user->name : 'Unknown',
                        'diamond_increase' => 0,
                        'bean_increase' => 0,
                        'increase_count' => 0,
                        'uid_count' => 0
                    ];
                }
                $userDetails[$userToken]['diamond_increase'] += $selfUidDiamondIncrease;
                $userDetails[$userToken]['bean_increase'] += $selfUidBeanIncrease;
                $userDetails[$userToken]['increase_count'] += $selfUidIncreaseCount;
                $userDetails[$userToken]['uid_count']++;
            }
        }

        // Sắp xếp selfUidDetails theo diamond_increase giảm dần và lấy top 20
        usort($selfUidDetails, function($a, $b) {
            return $b['diamond_increase'] - $a['diamond_increase'];
        });
        $selfUidDetails = array_slice($selfUidDetails, 0, 20);

        // Chuyển userDetails từ associative array thành indexed array và sắp xếp
        $userDetailsArray = array_values($userDetails);
        usort($userDetailsArray, function($a, $b) {
            return $b['diamond_increase'] - $a['diamond_increase'];
        });

        return [
            'total_diamond_increase' => $totalDiamondIncrease,
            'total_bean_increase' => $totalBeanIncrease,
            'total_increase_count' => $totalIncreaseCount,
            'affected_uid_count' => count($selfUidDetails),
            'user_details' => array_slice($userDetailsArray, 0, 10), // Top 10 users
            'self_uid_details' => $selfUidDetails // Top 20 Self UIDs
        ];
    }

    /**
     * Hiển thị danh sách tất cả selfUid tracking
     */
    public function index(Request $request)
    {
        // Lấy danh sách tracking với pagination
        $query = SelfUidTracking::with('user');
        $trackings = $this->applyFilters($query, $request)
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        // Lấy thống kê tổng quan
        $overallStats = $this->getOverallStats($request);

        // Lấy thống kê theo ngày
        $dailyStats = $this->getDailyStats($request);

        $users = User::all();

        return view('admin.selfuid-tracking.index', compact(
            'trackings',
            'users',
            'overallStats',
            'dailyStats'
        ));
    }

    /**
     * Hiển thị chi tiết lịch sử của một selfUid
     */
    public function show($selfUid, Request $request)
    {
        $query = SelfUidTracking::where('self_uid', $selfUid);

        // Filter theo user_token nếu có
        if ($request->filled('user_token')) {
            $query->where('user_token', $request->user_token);
        }

        // Filter theo ngày
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $history = $query->orderBy('created_at', 'desc')->paginate(100);

        // Lấy thống kê
        $stats = $this->getSelfUidStats($selfUid);

        return view('admin.selfuid-tracking.show', compact('history', 'selfUid', 'stats'));
    }

    /**
     * Hiển thị thống kê theo user_token
     */
    public function userStats($userToken)
    {
        $user = User::where('user_token', $userToken)->firstOrFail();
        
        // Thống kê tổng quan
        $stats = SelfUidTracking::getStatsByUserToken($userToken);
        
        // Top selfUid theo số lần request
        $topSelfUids = SelfUidTracking::where('user_token', $userToken)
            ->selectRaw('self_uid, COUNT(*) as request_count, MAX(created_at) as last_seen')
            ->groupBy('self_uid')
            ->orderBy('request_count', 'desc')
            ->limit(20)
            ->get();

        // Thống kê theo ngày (7 ngày gần nhất)
        $dailyStats = SelfUidTracking::where('user_token', $userToken)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('DATE(created_at) as date, COUNT(*) as requests, COUNT(DISTINCT self_uid) as unique_users')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return view('admin.selfuid-tracking.user-stats', compact('user', 'stats', 'topSelfUids', 'dailyStats'));
    }

    /**
     * API endpoint để lấy dữ liệu cho chart
     */
    public function chartData($selfUid, Request $request)
    {
        $days = $request->get('days', 7);
        $startDate = Carbon::now()->subDays($days);

        $data = SelfUidTracking::where('self_uid', $selfUid)
            ->where('created_at', '>=', $startDate)
            ->orderBy('created_at', 'asc')
            ->get();

        $chartData = [
            'labels' => [],
            'diamond' => [],
            'bean' => [],
            'room_count' => []
        ];

        foreach ($data as $item) {
            $chartData['labels'][] = $item->created_at->format('Y-m-d H:i');
            $chartData['diamond'][] = $item->diamond_balance;
            $chartData['bean'][] = $item->bean_balance;
            $chartData['room_count'][] = $item->room_count;
        }

        return response()->json($chartData);
    }

    /**
     * Lấy thống kê của một selfUid
     */
    private function getSelfUidStats($selfUid)
    {
        $latest = SelfUidTracking::where('self_uid', $selfUid)
            ->orderBy('created_at', 'desc')
            ->first();

        $earliest = SelfUidTracking::where('self_uid', $selfUid)
            ->orderBy('created_at', 'asc')
            ->first();

        $totalRequests = SelfUidTracking::where('self_uid', $selfUid)->count();

        $stats = [
            'total_requests' => $totalRequests,
            'first_seen' => $earliest ? $earliest->created_at : null,
            'last_seen' => $latest ? $latest->created_at : null,
            'current_diamond' => $latest ? $latest->diamond_balance : 0,
            'current_bean' => $latest ? $latest->bean_balance : 0,
            'current_room_count' => $latest ? $latest->room_count : 0,
        ];

        if ($earliest && $latest && $earliest->id !== $latest->id) {
            $stats['diamond_change'] = $latest->diamond_balance - $earliest->diamond_balance;
            $stats['bean_change'] = $latest->bean_balance - $earliest->bean_balance;
            $stats['room_count_change'] = $latest->room_count - $earliest->room_count;
        }

        return $stats;
    }

    /**
     * Xóa dữ liệu tracking cũ (để dọn dẹp database)
     */
    public function cleanup(Request $request)
    {
        $days = $request->get('days', 30);
        $cutoffDate = Carbon::now()->subDays($days);

        $deletedCount = SelfUidTracking::where('created_at', '<', $cutoffDate)->delete();

        return response()->json([
            'success' => true,
            'deleted_count' => $deletedCount,
            'message' => "Đã xóa {$deletedCount} bản ghi cũ hơn {$days} ngày"
        ]);
    }

    /**
     * Hiển thị thống kê tối ưu database
     */
    public function optimizationStats()
    {
        // Thống kê tổng quan
        $totalRecords = SelfUidTracking::count();
        $uniqueSelfUids = SelfUidTracking::distinct('self_uid')->count();
        $uniqueUserTokens = SelfUidTracking::distinct('user_token')->count();

        // Thống kê client cũ vs mới
        $oldClientRecords = SelfUidTracking::where('is_old_client', true)->count();
        $newClientRecords = SelfUidTracking::where('is_old_client', false)->count();

        // Thống kê theo ngày (7 ngày gần nhất)
        $dailyStats = SelfUidTracking::where('created_at', '>=', Carbon::now()->subDays(7))
            ->selectRaw('
                DATE(created_at) as date,
                COUNT(*) as records_saved,
                SUM(CASE WHEN is_old_client = 1 THEN 1 ELSE 0 END) as old_client_records,
                SUM(CASE WHEN is_old_client = 0 THEN 1 ELSE 0 END) as new_client_records
            ')
            ->groupBy('date')
            ->orderBy('date', 'desc')
            ->get();

        return response()->json([
            'total_records' => $totalRecords,
            'unique_self_uids' => $uniqueSelfUids,
            'unique_user_tokens' => $uniqueUserTokens,
            'old_client_records' => $oldClientRecords,
            'new_client_records' => $newClientRecords,
            'old_client_percentage' => $totalRecords > 0 ? round(($oldClientRecords / $totalRecords) * 100, 2) : 0,
            'daily_stats' => $dailyStats,
            'optimization_note' => 'Chỉ lưu khi có thay đổi Diamond/Bean/Room Count, hỗ trợ client cũ'
        ]);
    }
}
