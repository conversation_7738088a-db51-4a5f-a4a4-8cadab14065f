@extends('layouts.master')

@section('title')
    <title>Thống kê User: {{ $user->name }} - Admin Panel</title>
@endsection

@section('content')

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div class="loading-text mt-3">
                <h5>Đang tải dữ liệu...</h5>
                <p class="text-muted"><PERSON><PERSON> lòng chờ trong giây lát</p>
            </div>
        </div>
    </div>

    <script>
        // Hiển thị loading ngay lập tức khi trang bắt đầu load
        document.addEventListener('DOMContentLoaded', function() {
            var loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        });
    </script>

<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Thống kê User: {{ $user->name }}</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('selfuid-tracking.index') }}">SelfUID Tracking</a></li>
                        <li class="breadcrumb-item active">{{ $user->name }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- User Info -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thông tin User</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Tên:</strong></td>
                                    <td>{{ $user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>User Token:</strong></td>
                                    <td><span class="badge badge-primary">{{ $user->user_token }}</span></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Tổng requests:</strong></td>
                                    <td><span class="badge badge-info">{{ $stats->total_requests ?? 0 }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Unique users:</strong></td>
                                    <td><span class="badge badge-success">{{ $stats->unique_users ?? 0 }}</span></td>
                                </tr>
                                <tr>
                                    <td><strong>Hoạt động cuối:</strong></td>
                                    <td>
                                        @if($stats && $stats->last_activity)
                                            {{ \Carbon\Carbon::parse($stats->last_activity)->format('d/m/Y H:i:s') }}
                                        @else
                                            Chưa có hoạt động
                                        @endif
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Daily Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thống kê 7 ngày gần nhất</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Ngày</th>
                                    <th>Số requests</th>
                                    <th>Unique users</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($dailyStats as $stat)
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($stat->date)->format('d/m/Y') }}</td>
                                        <td><span class="badge badge-info">{{ $stat->requests }}</span></td>
                                        <td><span class="badge badge-success">{{ $stat->unique_users }}</span></td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Top SelfUIDs -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Top 20 SelfUID hoạt động nhiều nhất</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Self UID</th>
                                    <th>Số lần request</th>
                                    <th>Lần cuối thấy</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($topSelfUids as $index => $selfUid)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $selfUid->self_uid) }}" 
                                               class="text-primary font-weight-bold">
                                                {{ $selfUid->self_uid }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $selfUid->request_count }}</span>
                                        </td>
                                        <td>
                                            <small>{{ \Carbon\Carbon::parse($selfUid->last_seen)->format('d/m/Y H:i:s') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $selfUid->self_uid) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="5" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Hành động nhanh</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <a href="{{ route('selfuid-tracking.index', ['user_token' => $user->user_token]) }}" 
                               class="btn btn-primary btn-block">
                                <i class="fas fa-list"></i> Xem tất cả tracking của user này
                            </a>
                        </div>
                        <div class="col-md-4">
                            <a href="{{ route('selfuid-tracking.index', ['user_token' => $user->user_token, 'date_from' => now()->format('Y-m-d')]) }}" 
                               class="btn btn-success btn-block">
                                <i class="fas fa-calendar-day"></i> Xem tracking hôm nay
                            </a>
                        </div>
                        <div class="col-md-4">
                            <button type="button" class="btn btn-warning btn-block" onclick="exportData()">
                                <i class="fas fa-download"></i> Export dữ liệu
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@section('script')
<script>
function exportData() {
    // Implement export functionality if needed
    alert('Chức năng export sẽ được phát triển trong tương lai');
}

$(document).ready(function() {
    // Ẩn loading sau khi DOM và tất cả nội dung đã load xong
    $(window).on('load', function() {
        hideLoading();
    });

    // Hiển thị loading khi click vào các link
    $(document).on('click', 'a[href*="selfuid-tracking"]', function(e) {
        // Chỉ hiển thị loading cho các link nội bộ, không phải link external
        if (this.hostname === window.location.hostname) {
            showLoading();
        }
    });

    function showLoading() {
        $('#loading-overlay').fadeIn(200);
    }

    function hideLoading() {
        $('#loading-overlay').fadeOut(300);
    }

    // Backup: Ẩn loading sau 1 giây nếu window.load không trigger
    setTimeout(function() {
        if ($('#loading-overlay').is(':visible')) {
            hideLoading();
        }
    }, 1000);
});
</script>

<style>
/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: none; /* Ẩn ban đầu */
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.loading-content {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e6f0;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.loading-text h5 {
    color: #5a5c69;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-text p {
    color: #858796;
    margin-bottom: 0;
    font-size: 14px;
}
</style>

@endsection
