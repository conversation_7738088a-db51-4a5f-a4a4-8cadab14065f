@extends('layouts.master')

@section('title')
    <title>{{ $title }} - {{ \Carbon\Carbon::parse($date)->format('d/m/Y') }} - Admin Panel</title>
@endsection

@section('content')

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div class="loading-text mt-3">
                <h5>Đang tải dữ liệu...</h5>
                <p class="text-muted"><PERSON>ui lòng chờ trong giây lát</p>
            </div>
        </div>
    </div>

    <script>
        // Hiển thị loading ngay lập tức khi trang bắt đầu load
        document.addEventListener('DOMContentLoaded', function() {
            var loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        });

        // Xử lý khi người dùng bấm back/forward (pageshow event)
        window.addEventListener('pageshow', function(event) {
            // Nếu trang được load từ cache (bfcache)
            if (event.persisted) {
                var loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        });
    </script>

    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">{{ $title }}</h1>
                    <p class="text-muted">Ngày: {{ \Carbon\Carbon::parse($date)->format('d/m/Y (l)') }}</p>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('selfuid-tracking.index') }}">SelfUID Tracking</a></li>
                        <li class="breadcrumb-item active">Thống kê dao động</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Statistics Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ $title }}</h3>
                    <div class="card-tools">
                        <a href="{{ route('selfuid-tracking.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tổng quan Tăng -->
                    <h5 class="mb-3 text-success"><i class="fas fa-arrow-up"></i> Thống kê tăng Diamond và Bean</h5>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-gem"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Diamond tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['total_diamond_increase']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-coins"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Bean tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['total_bean_increase']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Số lần tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['total_increase_count']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Self UID tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['affected_uid_count']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tổng quan Giảm -->
                    <h5 class="mb-3 text-danger"><i class="fas fa-arrow-down"></i> Thống kê giảm Diamond và Bean</h5>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-gem"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Diamond giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['total_diamond_decrease']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-coins"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Bean giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['total_bean_decrease']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Số lần giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['total_decrease_count']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-secondary">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Self UID giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['affected_uid_count']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chi tiết theo Self UID - Tăng -->
                    @if(count($increaseStats['self_uid_details']) > 0)
                    <h5 class="mb-3 text-success">Top 20 Self UID có lượng tăng Diamond nhiều nhất</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Self UID</th>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond tăng</th>
                                    <th>Bean tăng</th>
                                    <th>Số lần tăng</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($increaseStats['self_uid_details'] as $index => $uidDetail)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="text-primary font-weight-bold">
                                                {{ $uidDetail['self_uid'] }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ $uidDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $uidDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($uidDetail['diamond_increase']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($uidDetail['bean_increase']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $uidDetail['increase_count'] }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif

                    <!-- Chi tiết theo Self UID - Giảm -->
                    @if(count($decreaseStats['self_uid_details']) > 0)
                    <h5 class="mb-3 mt-4 text-danger">Top 20 Self UID có lượng giảm Diamond nhiều nhất</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Self UID</th>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond giảm</th>
                                    <th>Bean giảm</th>
                                    <th>Số lần giảm</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($decreaseStats['self_uid_details'] as $index => $uidDetail)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="text-primary font-weight-bold">
                                                {{ $uidDetail['self_uid'] }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ $uidDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $uidDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-danger">
                                                {{ number_format($uidDetail['diamond_decrease']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($uidDetail['bean_decrease']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $uidDetail['decrease_count'] }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif

                    <!-- Tóm tắt theo User - Tăng -->
                    @if(count($increaseStats['user_details']) > 0)
                    <h5 class="mb-3 mt-4 text-success">Tóm tắt theo User - Tăng</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond tăng</th>
                                    <th>Bean tăng</th>
                                    <th>Self UID ảnh hưởng</th>
                                    <th>Số lần tăng</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($increaseStats['user_details'] as $userDetail)
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary">{{ $userDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $userDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($userDetail['diamond_increase']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($userDetail['bean_increase']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $userDetail['uid_count'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $userDetail['increase_count'] }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif

                    <!-- Tóm tắt theo User - Giảm -->
                    @if(count($decreaseStats['user_details']) > 0)
                    <h5 class="mb-3 mt-4 text-danger">Tóm tắt theo User - Giảm</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond giảm</th>
                                    <th>Bean giảm</th>
                                    <th>Self UID ảnh hưởng</th>
                                    <th>Số lần giảm</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($decreaseStats['user_details'] as $userDetail)
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary">{{ $userDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $userDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-danger">
                                                {{ number_format($userDetail['diamond_decrease']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($userDetail['bean_decrease']) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $userDetail['uid_count'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $userDetail['decrease_count'] }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </section>

@endsection

@section('script')
<script>
$(document).ready(function() {
    // Ẩn loading sau khi DOM và tất cả nội dung đã load xong
    $(window).on('load', function() {
        hideLoading();
    });

    // Xử lý khi trang được hiển thị (bao gồm cả khi back/forward)
    $(window).on('pageshow', function(event) {
        if (event.originalEvent.persisted) {
            hideLoading();
        }
    });

    // Hiển thị loading khi click vào các link
    $(document).on('click', 'a[href*="selfuid-tracking"]', function(e) {
        // Chỉ hiển thị loading cho các link nội bộ, không phải link external
        if (this.hostname === window.location.hostname) {
            showLoading();
        }
    });

    function showLoading() {
        $('#loading-overlay').fadeIn(200);
    }

    function hideLoading() {
        $('#loading-overlay').fadeOut(300);
    }

    // Backup: Ẩn loading sau 1 giây nếu window.load không trigger
    setTimeout(function() {
        if ($('#loading-overlay').is(':visible')) {
            hideLoading();
        }
    }, 1000);
});
</script>

<style>
/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: none; /* Ẩn ban đầu */
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.loading-content {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e6f0;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.loading-text h5 {
    color: #5a5c69;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-text p {
    color: #858796;
    margin-bottom: 0;
    font-size: 14px;
}
</style>

@endsection
