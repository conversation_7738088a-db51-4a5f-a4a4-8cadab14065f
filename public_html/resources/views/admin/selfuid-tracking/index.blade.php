@extends('layouts.master')

@section('title')
    <title>SelfUID Tracking - Admin Panel</title>
@endsection

@section('content')

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: flex;">
        <div class="loading-content">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <div class="loading-text mt-3">
                <h5>Đang tải dữ liệu...</h5>
                <p class="text-muted"><PERSON>ui lòng chờ trong giây lát</p>
            </div>
        </div>
    </div>

    <script>
        // Hiển thị loading ngay lập tức khi trang bắt đầu load
        document.addEventListener('DOMContentLoaded', function() {
            var loadingOverlay = document.getElementById('loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        });

        // Xử lý khi người dùng bấm back/forward (pageshow event)
        window.addEventListener('pageshow', function(event) {
            // Nếu trang được load từ cache (bfcache)
            if (event.persisted) {
                var loadingOverlay = document.getElementById('loading-overlay');
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
            }
        });
    </script>

    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">SelfUID Tracking</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">SelfUID Tracking</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Optimization Info -->

            <!-- Filter Form -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Bộ lọc</h3>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('selfuid-tracking.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>User Token</label>
                                    <select name="user_token" class="form-control select2">
                                        <option value="">-- Tất cả --</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->user_token }}" 
                                                {{ request('user_token') == $user->user_token ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->user_token }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Self UID</label>
                                    <input type="text" name="self_uid" class="form-control" 
                                           value="{{ request('self_uid') }}" placeholder="Tìm theo Self UID">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Từ ngày</label>
                                    <input type="date" name="date_from" class="form-control" 
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Đến ngày</label>
                                    <input type="date" name="date_to" class="form-control" 
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary" id="search-btn">
                                            <i class="fas fa-search"></i> Tìm kiếm
                                        </button>
                                        <a href="{{ route('selfuid-tracking.index') }}" class="btn btn-secondary" id="reset-btn">
                                            <i class="fas fa-undo"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Daily Statistics -->
            @if($dailyStats->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thống kê theo ngày</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Ngày</th>
                                    <th>Self UID</th>
                                    <th>Tổng Diamond</th>
                                    <th>Số Diamond dao động</th>
                                    <th>Tổng Bean</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($dailyStats as $stat)
                                    <tr>
                                        <!-- ngày -->
                                        <td>
                                            <strong>{{ \Carbon\Carbon::parse($stat->date)->format('d/m/Y') }}</strong>
                                            <br><small class="text-muted">{{ \Carbon\Carbon::parse($stat->date)->format('l') }}</small>
                                        </td>
                                        <!-- unique_self_uids -->
                                        <td>
                                            <span class="badge badge-success">{{ $stat->unique_self_uids }}</span>
                                        </td>
                                        <!-- total_diamond -->
                                        <td>
                                            <span class="badge badge-warning">{{ number_format($stat->total_diamond) }}</span>
                                        </td>
                                        <!-- diamond_change -->
                                        <td>
                                            @if(isset($stat->diamond_change) && $stat->diamond_change != 0)
                                                <div class="text-center">
                                                    <a href="{{ route('selfuid-tracking.daily-stats', ['date' => $stat->date]) }}"
                                                       class="text-decoration-none">
                                                        @if($stat->diamond_change > 0)
                                                            <span class="badge badge-success badge-sm">
                                                                <i class="fas fa-gem"></i> +{{ number_format($stat->diamond_change) }}
                                                            </span>
                                                        @else
                                                            <span class="badge badge-danger badge-sm">
                                                                <i class="fas fa-gem"></i> {{ number_format($stat->diamond_change) }}
                                                            </span>
                                                        @endif
                                                    </a>
                                                    <br>
                                                    <small class="text-muted">Click để xem chi tiết</small>
                                                </div>
                                            @else
                                                <span class="badge badge-light">Không có thay đổi</span>
                                            @endif
                                        </td>
                                        <!-- total_bean -->
                                        <td>
                                            <span class="badge badge-info">{{ number_format($stat->total_bean) }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif



            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Danh sách SelfUID Tracking</h3>
                    <div class="card-tools">
                        <span class="badge badge-success">{{ $overallStats['uniqueSelfUidsCount'] }} Self UID</span>
                        <span class="badge badge-warning ml-1">{{ number_format($overallStats['totalDiamond']) }} Diamond</span>
                        <span class="badge badge-info ml-1">{{ number_format($overallStats['totalBean']) }} Bean</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User Token</th>
                                    <th>Self UID</th>
                                    <th>Diamond</th>
                                    <th>Bean</th>
                                    <th>Room Count</th>
                                    <th>IP Address</th>
                                    <th>Thời gian</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($trackings as $tracking)
                                    <tr>
                                        <td>{{ $tracking->id }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ $tracking->user_token }}</span>
                                            @if($tracking->user)
                                                <br><small>{{ $tracking->user->name }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $tracking->self_uid) }}" 
                                               class="text-primary font-weight-bold">
                                                {{ $tracking->self_uid }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($tracking->diamond_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($tracking->bean_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ $tracking->room_count }}
                                            </span>
                                        </td>
                                        <td>{{ $tracking->ip_address }}</td>
                                        <td>
                                            <small>{{ $tracking->created_at->format('d/m/Y H:i:s') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $tracking->self_uid) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {{ $trackings->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </section>

@endsection

@section('script')
<script>
$(document).ready(function() {
    $('.select2').select2({
        theme: 'bootstrap4'
    });

    // Ẩn loading sau khi DOM và tất cả nội dung đã load xong
    $(window).on('load', function() {
        hideLoading();
    });

    // Xử lý khi trang được hiển thị (bao gồm cả khi back/forward)
    $(window).on('pageshow', function(event) {
        if (event.originalEvent.persisted) {
            hideLoading();
        }
    });

    // Hiển thị loading khi submit form tìm kiếm
    $('#search-btn').on('click', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click reset
    $('#reset-btn').on('click', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click pagination links
    $(document).on('click', '.pagination a', function(e) {
        showLoading();
    });

    // Hiển thị loading khi click vào các link chi tiết
    $(document).on('click', 'a[href*="selfuid-tracking"]', function(e) {
        // Chỉ hiển thị loading cho các link nội bộ, không phải link external
        if (this.hostname === window.location.hostname) {
            showLoading();
        }
    });

    function showLoading() {
        $('#loading-overlay').fadeIn(200);
    }

    function hideLoading() {
        $('#loading-overlay').fadeOut(300);
    }

    // Backup: Ẩn loading sau 1 giây nếu window.load không trigger
    setTimeout(function() {
        if ($('#loading-overlay').is(':visible')) {
            hideLoading();
        }
    }, 1000);
});
</script>
<style>
/* Loading Overlay Styles */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    z-index: 9999;
    display: none; /* Ẩn ban đầu */
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(2px);
}

.loading-content {
    text-align: center;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #e3e6f0;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

.loading-text h5 {
    color: #5a5c69;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-text p {
    color: #858796;
    margin-bottom: 0;
    font-size: 14px;
}

.pagination {
    margin: 0;
    justify-content: center;
}
.pagination .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border: 1px solid #dee2e6;
    color: #6c757d;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pagination .page-item {
    margin: 0 1px;
}
.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    font-weight: 500;
}
.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}
/* Fix SVG icon size với selector mạnh hơn */
.card-footer .pagination .page-link svg,
.pagination .page-link svg.w-5,
.pagination .page-link svg.h-5 {
    width: 12px !important;
    height: 12px !important;
    max-width: 12px !important;
    max-height: 12px !important;
}
/* Override Tailwind classes */
.w-5, .h-5 {
    width: 12px !important;
    height: 12px !important;
}
/* Select2 styling */
.select2-container--bootstrap4 .select2-selection--single {
    border: 2px solid #ced4da !important;
    border-radius: 0.375rem !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.select2-container--bootstrap4 .select2-selection--single:hover {
    border-color: #007bff !important;
    cursor: pointer;
}
.select2-container--bootstrap4.select2-container--focus .select2-selection--single {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}
</style>
@endsection
