@extends('layouts.master')

@section('title')
    <title>SelfUID Tracking - Admin Panel</title>
@endsection

@section('content')

    <!-- Content Header (Page header) -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">SelfUID Tracking</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="#">Home</a></li>
                        <li class="breadcrumb-item active">SelfUID Tracking</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Optimization Info -->

            <!-- Filter Form -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Bộ lọc</h3>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('selfuid-tracking.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>User Token</label>
                                    <select name="user_token" class="form-control select2">
                                        <option value="">-- Tất cả --</option>
                                        @foreach($users as $user)
                                            <option value="{{ $user->user_token }}" 
                                                {{ request('user_token') == $user->user_token ? 'selected' : '' }}>
                                                {{ $user->name }} ({{ $user->user_token }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Self UID</label>
                                    <input type="text" name="self_uid" class="form-control" 
                                           value="{{ request('self_uid') }}" placeholder="Tìm theo Self UID">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Từ ngày</label>
                                    <input type="date" name="date_from" class="form-control" 
                                           value="{{ request('date_from') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Đến ngày</label>
                                    <input type="date" name="date_to" class="form-control" 
                                           value="{{ request('date_to') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> Tìm kiếm
                                        </button>
                                        <a href="{{ route('selfuid-tracking.index') }}" class="btn btn-secondary">
                                            <i class="fas fa-undo"></i> Reset
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Daily Statistics -->
            @if($dailyStats->count() > 0)
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thống kê theo ngày</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Ngày</th>
                                    <th>Self UID</th>
                                    <th>Tổng Diamond</th>
                                    <th>Số Diamond dao động</th>
                                    <th>Tăng trong uid</th>
                                    <th>Tổng Bean</th>
                                    <th>Tăng Bean trong ngày</th>
                                    <th>Giảm trong ngày</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($dailyStats as $stat)
                                    <tr>
                                        <!-- ngày -->
                                        <td>
                                            <strong>{{ \Carbon\Carbon::parse($stat->date)->format('d/m/Y') }}</strong>
                                            <br><small class="text-muted">{{ \Carbon\Carbon::parse($stat->date)->format('l') }}</small>
                                        </td>
                                        <!-- unique_self_uids -->
                                        <td>
                                            <span class="badge badge-success">{{ $stat->unique_self_uids }}</span>
                                        </td>
                                        <!-- total_diamond -->
                                        <td>
                                            <span class="badge badge-warning">{{ number_format($stat->total_diamond) }}</span>
                                        </td>
                                        <!-- diamond_change -->
                                        <td>
                                            @if(isset($stat->diamond_change) && $stat->diamond_change > 0)
                                                <div class="text-center">
                                                    <span class="badge badge-success badge-sm">
                                                        <i class="fas fa-gem"></i> +{{ number_format($stat->diamond_change) }}
                                                    </span>
                                                </div>
                                            @elseif(isset($stat->diamond_change) && $stat->diamond_change < 0)
                                                <div class="text-center">
                                                    <span class="badge badge-danger badge-sm">
                                                        <i class="fas fa-gem"></i> {{ number_format($stat->diamond_change) }}
                                                    </span>
                                                </div>
                                            @else
                                                <span class="badge badge-light">Không có thay đổi</span>
                                            @endif
                                        </td>
                                        <!-- daily_diamond_increase -->
                                        <td>
                                            @if(isset($stat->daily_diamond_increase) && $stat->daily_diamond_increase > 0)
                                                <div class="text-center">
                                                    <span class="badge badge-success badge-sm">
                                                        <i class="fas fa-gem"></i> +{{ number_format($stat->daily_diamond_increase) }}
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ $stat->affected_increase_uids_count ?? 0 }} UID, {{ $stat->daily_increase_count ?? 0 }} lần
                                                    </small>
                                                </div>
                                            @else
                                                <span class="badge badge-light">Không có tăng</span>
                                            @endif
                                        </td>
                                        <!-- total_bean -->
                                        <td>
                                            <span class="badge badge-info">{{ number_format($stat->total_bean) }}</span>
                                        </td>
                                        <!-- daily_bean_increase -->
                                        <td>
                                            @if(isset($stat->daily_bean_increase) && $stat->daily_bean_increase > 0)
                                                <div class="text-center">
                                                    <span class="badge badge-success badge-sm">
                                                        <i class="fas fa-coins"></i> +{{ number_format($stat->daily_bean_increase) }}
                                                    </span>
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ $stat->affected_increase_uids_count ?? 0 }} UID, {{ $stat->daily_increase_count ?? 0 }} lần
                                                    </small>
                                                </div>
                                            @else
                                                <span class="badge badge-light">Không có tăng</span>
                                            @endif
                                        </td>
                                        <!-- daily_diamond_decrease -->
                                        <td>
                                            @if($stat->daily_diamond_decrease > 0 || $stat->daily_bean_decrease > 0)
                                                <div class="text-center">
                                                    @if($stat->daily_diamond_decrease > 0)
                                                        <span class="badge badge-danger badge-sm">
                                                            <i class="fas fa-gem"></i> -{{ number_format($stat->daily_diamond_decrease) }}
                                                        </span>
                                                    @endif
                                                    @if($stat->daily_bean_decrease > 0)
                                                        <span class="badge badge-warning badge-sm">
                                                            <i class="fas fa-coins"></i> -{{ number_format($stat->daily_bean_decrease) }}
                                                        </span>
                                                    @endif
                                                    <br>
                                                    <small class="text-muted">
                                                        {{ $stat->affected_uids_count }} UID, {{ $stat->daily_decrease_count }} lần
                                                    </small>
                                                </div>
                                            @else
                                                <span class="badge badge-light">Không có giảm</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif

            <!-- Decrease Statistics -->
            @if(isset($decreaseStats) && ($decreaseStats['total_diamond_decrease'] > 0 || $decreaseStats['total_bean_decrease'] > 0))
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thống kê tổng lượng giảm Diamond và Bean</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tổng quan -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="info-box bg-danger">
                                <span class="info-box-icon"><i class="fas fa-gem"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Diamond giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['total_diamond_decrease']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-coins"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Bean giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['total_bean_decrease']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Self UID bị ảnh hưởng</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['affected_uid_count']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-secondary">
                                <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Số lần giảm</span>
                                    <span class="info-box-number">{{ number_format($decreaseStats['total_decrease_count']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chi tiết theo Self UID -->
                    @if(count($decreaseStats['self_uid_details']) > 0)
                    <h5 class="mb-3">Top 20 Self UID có lượng giảm Diamond nhiều nhất</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Self UID</th>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond giảm</th>
                                    <th>Bean giảm</th>
                                    <th>Số lần giảm</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($decreaseStats['self_uid_details'] as $index => $uidDetail)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="text-primary font-weight-bold">
                                                {{ $uidDetail['self_uid'] }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ $uidDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $uidDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-danger">{{ number_format($uidDetail['diamond_decrease']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">{{ number_format($uidDetail['bean_decrease']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $uidDetail['decrease_count'] }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif

                    <!-- Chi tiết theo user (thu gọn) -->
                    @if(count($decreaseStats['user_details']) > 0)
                    <h5 class="mb-3 mt-4">Tóm tắt theo User</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond giảm</th>
                                    <th>Bean giảm</th>
                                    <th>Self UID ảnh hưởng</th>
                                    <th>Số lần giảm</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($decreaseStats['user_details'] as $userDetail)
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary">{{ $userDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $userDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-danger">{{ number_format($userDetail['diamond_decrease']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">{{ number_format($userDetail['bean_decrease']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $userDetail['uid_count'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $userDetail['decrease_count'] }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Increase Statistics -->
            @if(isset($increaseStats) && ($increaseStats['total_diamond_increase'] > 0 || $increaseStats['total_bean_increase'] > 0))
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Thống kê tổng lượng tăng Diamond và Bean</h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-card-widget="collapse">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Tổng quan -->
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <div class="info-box bg-success">
                                <span class="info-box-icon"><i class="fas fa-gem"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Diamond tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['total_diamond_increase']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-warning">
                                <span class="info-box-icon"><i class="fas fa-coins"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Tổng Bean tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['total_bean_increase']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-info">
                                <span class="info-box-icon"><i class="fas fa-chart-line"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Số lần tăng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['total_increase_count']) }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-primary">
                                <span class="info-box-icon"><i class="fas fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Self UID ảnh hưởng</span>
                                    <span class="info-box-number">{{ number_format($increaseStats['affected_uid_count']) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Chi tiết theo Self UID -->
                    @if(count($increaseStats['self_uid_details']) > 0)
                    <h5 class="mb-3">Top 20 Self UID có lượng tăng Diamond nhiều nhất</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Self UID</th>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond tăng</th>
                                    <th>Bean tăng</th>
                                    <th>Số lần tăng</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($increaseStats['self_uid_details'] as $index => $uidDetail)
                                    <tr>
                                        <td>{{ $index + 1 }}</td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="text-primary font-weight-bold">
                                                {{ $uidDetail['self_uid'] }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-primary">{{ $uidDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $uidDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-success">{{ number_format($uidDetail['diamond_increase']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">{{ number_format($uidDetail['bean_increase']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $uidDetail['increase_count'] }}</span>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $uidDetail['self_uid']) }}"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif

                    <!-- Chi tiết theo user (thu gọn) -->
                    @if(count($increaseStats['user_details']) > 0)
                    <h5 class="mb-3 mt-4">Tóm tắt theo User</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>User Token</th>
                                    <th>Tên User</th>
                                    <th>Diamond tăng</th>
                                    <th>Bean tăng</th>
                                    <th>Self UID ảnh hưởng</th>
                                    <th>Số lần tăng</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($increaseStats['user_details'] as $userDetail)
                                    <tr>
                                        <td>
                                            <span class="badge badge-primary">{{ $userDetail['user_token'] }}</span>
                                        </td>
                                        <td>{{ $userDetail['user_name'] }}</td>
                                        <td>
                                            <span class="badge badge-success">{{ number_format($userDetail['diamond_increase']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">{{ number_format($userDetail['bean_increase']) }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ $userDetail['uid_count'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ $userDetail['increase_count'] }}</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif
                </div>
            </div>
            @endif

            <!-- Data Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Danh sách SelfUID Tracking</h3>
                    <div class="card-tools">
                        <span class="badge badge-success">{{ $overallStats['uniqueSelfUidsCount'] }} Self UID</span>
                        <span class="badge badge-warning ml-1">{{ number_format($overallStats['totalDiamond']) }} Diamond</span>
                        <span class="badge badge-info ml-1">{{ number_format($overallStats['totalBean']) }} Bean</span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User Token</th>
                                    <th>Self UID</th>
                                    <th>Diamond</th>
                                    <th>Bean</th>
                                    <th>Room Count</th>
                                    <th>IP Address</th>
                                    <th>Thời gian</th>
                                    <th>Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($trackings as $tracking)
                                    <tr>
                                        <td>{{ $tracking->id }}</td>
                                        <td>
                                            <span class="badge badge-primary">{{ $tracking->user_token }}</span>
                                            @if($tracking->user)
                                                <br><small>{{ $tracking->user->name }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $tracking->self_uid) }}" 
                                               class="text-primary font-weight-bold">
                                                {{ $tracking->self_uid }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge badge-warning">
                                                {{ number_format($tracking->diamond_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-success">
                                                {{ number_format($tracking->bean_balance) }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ $tracking->room_count }}
                                            </span>
                                        </td>
                                        <td>{{ $tracking->ip_address }}</td>
                                        <td>
                                            <small>{{ $tracking->created_at->format('d/m/Y H:i:s') }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('selfuid-tracking.show', $tracking->self_uid) }}" 
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="10" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer">
                    {{ $trackings->appends(request()->query())->links() }}
                </div>
            </div>
        </div>
    </section>

@endsection

@section('script')
<script>
$(document).ready(function() {
    $('.select2').select2({
        theme: 'bootstrap4'
    });
});
</script>
<style>
.pagination {
    margin: 0;
    justify-content: center;
}
.pagination .page-link {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border: 1px solid #dee2e6;
    color: #6c757d;
    min-width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pagination .page-item {
    margin: 0 1px;
}
.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    font-weight: 500;
}
.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #495057;
}
/* Fix SVG icon size với selector mạnh hơn */
.card-footer .pagination .page-link svg,
.pagination .page-link svg.w-5,
.pagination .page-link svg.h-5 {
    width: 12px !important;
    height: 12px !important;
    max-width: 12px !important;
    max-height: 12px !important;
}
/* Override Tailwind classes */
.w-5, .h-5 {
    width: 12px !important;
    height: 12px !important;
}
/* Select2 styling */
.select2-container--bootstrap4 .select2-selection--single {
    border: 2px solid #ced4da !important;
    border-radius: 0.375rem !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.select2-container--bootstrap4 .select2-selection--single:hover {
    border-color: #007bff !important;
    cursor: pointer;
}
.select2-container--bootstrap4.select2-container--focus .select2-selection--single {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}
</style>
@endsection
